const { getDefaultConfig, mergeConfig } = require('@react-native/metro-config');

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('@react-native/metro-config').MetroConfig}
 */

// Check if obfuscation should be enabled
const enableObfuscation = process.env.ENABLE_OBFUSCATION === 'true' || process.env.NODE_ENV === 'production';

let config = {};

if (enableObfuscation) {
  config = {
    transformer: {
      getTransformOptions: async () => ({
        transform: {
          experimentalImportSupport: false,
          inlineRequires: false,
        },
      }),
      babelTransformerPath: require.resolve("./transformer")
    },
  };
} else {
  config = {};
}

module.exports = mergeConfig(getDefaultConfig(__dirname), config);