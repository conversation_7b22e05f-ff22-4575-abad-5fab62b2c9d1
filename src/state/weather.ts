import { createSlice, PayloadAction, createAsyncThunk } from '@reduxjs/toolkit'
import { GET } from '../requests/httpClient'
import { WeatherResponse } from '../data/ForcastCardProps'

interface WeatherState {
  weather: WeatherResponse | null
  loading: boolean
  error: string | null
}

const initialState: WeatherState = {
  weather: null,
  loading: false,
  error: null,
}

export const weatherAsync = createAsyncThunk('weather/fetchWeather', async (city: string) => {
  const response = await GET(`/forecast?q=${city}`, {})
  return response as WeatherResponse
})

const weatherSlice = createSlice({
  name: 'weather',
  initialState,
  reducers: {
  },
  extraReducers: (builder) => {
    builder.addCase(weatherAsync.fulfilled, (state, action: PayloadAction<WeatherResponse>) => {
      state.weather = action.payload
      state.loading = false
      state.error = null
    }).addCase(weatherAsync.pending, (state) => {
      state.loading = true
      state.error = null
    }).addCase(weatherAsync.rejected, (state, action) => {
      state.loading = false
      state.error = action.error.message || 'An error occurred'
    })
  }
})

export default weatherSlice.reducer