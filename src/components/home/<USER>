import { View, Text, Button, ActivityIndicator } from 'react-native';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { WeatherParamList } from '../../data/WeatherParamList';
import homeStyles from '../../styles/home';
// import useWeatherRequest from '../../requests/request';
import { ForcastList } from './ForcastCard';
import SearchBar from '../search/SearchBar';
import { useDispatch, useSelector } from 'react-redux';
import FilterCityList from './FilterCityList';
import { useEffect, useState } from 'react';
import { addFavorite } from '../../state/favoriteSlice';
import showToast from '../../tools/toast';
import { weatherAsync } from '../../state/weather';
import { RootState, AppDispatch } from '../../state/store';
import React from 'react';

type HomeScreenProps = NativeStackScreenProps<WeatherParamList, 'HomeScreen'> 
export default function HomeScreen({ navigation }: HomeScreenProps) {

  const [city, setCity] = useState('New York');
  const [isSearching, setIsSearching] = useState(false);
  const [searchText, setSearchText] = useState('');
  const dispatch = useDispatch<AppDispatch>();

  const {weather, loading, error} = useSelector((state: RootState) => state.weather);

    useEffect(() => {
      dispatch(weatherAsync(city))
    }, [city])

  return (
    <View style={homeStyles.container}>
      <SearchBar searchTextUpdated={(text) => {
        setSearchText(text)
        if (text.length > 0) {
          setIsSearching(true)
        } else {
          setIsSearching(false)
        }
      }} />
      {
        isSearching && (<FilterCityList onCitySelected={(city) => {
          console.log("city", city)
          setSearchText("")
          setIsSearching(false)
          setCity(city)
        }} city={searchText} />)
      }
      {
        !isSearching && (
          <View style={homeStyles.contentContainer}>
            <Text style={homeStyles.homeHeader}>Current Selected City: {city}</Text>
            {loading && (
                <View>
                  <ActivityIndicator size="large" color="#0000ff" />
                </View>
            )}
            {error && (
              <View>
                <Text style={homeStyles.errorText}>Error: {error}</Text>
              </View>
            )}
            {weather != null && <ForcastList weatherList={weather.list} />}

          </View>
        )
      }

      <Button
        title="Add to Favorite"
        onPress={() => {
          dispatch(addFavorite(city));
          showToast(`${city} added to favorite`);
        }}
      />

      <Button
        title="Go to Favorite"
        onPress={() => {
          navigation.navigate('Favorite', { location: city });
        }}
      />
    </View>
  );
}
