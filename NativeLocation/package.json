{"name": "NativeWeatherLocation", "version": "0.0.1", "description": "Native Weather Location", "react-native": "js/index", "source": "js/index", "files": ["js", "android", "ios", "rtn-calculator.podspec", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/damon/NativeWeatherLocation", "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/damon/NativeWeatherLocation/issues"}, "homepage": "https://github.com/damon/NativeWeatherLocation#readme", "devDependencies": {}, "peerDependencies": {"react": "*", "react-native": "*"}, "codegenConfig": {"name": "NativeWeatherLocationSpec", "type": "modules", "jsSrcsDir": "./specs", "android": {"javaPackageName": "com.nativeweatherlocation"}}}