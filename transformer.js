const obfuscatingTransformer = require("react-native-obfuscating-transformer");

const isProduction = process.env.NODE_ENV === 'production';

const filter = (filename) => {
  // Only obfuscate src files and only in production
  return isProduction && filename.startsWith("src");
};

module.exports = obfuscatingTransformer({
  obfuscatorOptions: {
    compact: true,
    controlFlowFlattening: false,
    controlFlowFlatteningThreshold: 0.75,
    deadCodeInjection: false,
    deadCodeInjectionThreshold: 0.4,
    debugProtection: false,
    debugProtectionInterval: false,
    disableConsoleOutput: false,
    domainLock: [],
    identifierNamesGenerator: "hexadecimal", // Changed from "mangled" to avoid constructor issues
    identifiersDictionary: [],
    identifiersPrefix: "",
    inputFileName: "",
    log: false, // Reduced logging
    renameGlobals: false,
    renameProperties: false, // Keep this false to avoid breaking object properties
    reservedNames: [
      // Reserve important names to avoid breaking functionality
      "constructor",
      "prototype",
      "__proto__",
      "length",
      "name",
      "toString",
      "valueOf",
      "hasOwnProperty",
      "isPrototypeOf",
      "propertyIsEnumerable",
      "React",
      "Component",
      "PureComponent",
      "createClass",
      "createElement",
      "cloneElement",
      "createFactory",
      "isValidElement",
      "PropTypes",
      "Children"
    ],
    reservedStrings: [],
    rotateStringArray: true,
    seed: 0,
    selfDefending: false, // Disabled to avoid issues
    shuffleStringArray: true,
    sourceMap: false,
    sourceMapBaseUrl: "",
    sourceMapFileName: "",
    sourceMapMode: "separate",
    splitStrings: false,
    splitStringsChunkLength: 10,
    stringArray: true,
    stringArrayEncoding: false,
    stringArrayThreshold: 0.75,
    target: "browser", // Changed from "node" to "browser" for React Native
    transformObjectKeys: false, // Disabled to avoid breaking object keys
    unicodeEscapeSequence: false,
  },
  upstreamTransformer: require("metro-react-native-babel-transformer"),
  emitObfuscatedFiles: false,
  enableInDevelopment: isProduction, // Only enable in production
  filter,
  trace: false, // Reduced tracing
});